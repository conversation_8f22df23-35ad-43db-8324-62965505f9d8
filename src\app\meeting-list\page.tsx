"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layout/mainlayout";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table/data-table";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/ui/progressiveLoader";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumb";
import { useTranslation } from "react-i18next";
import { Plus } from "lucide-react";
import { Combobox } from "@/components/ui/combobox";
import type {
  InnerItem,
  ToastType,
  ComboData,
  LiveClassResponse,
} from "@/types";
import { ERROR_MESSAGES } from "@/lib/messages";
import { ORG_KEY, pageUrl } from "@/lib/constants";
import useLiveClass from "@/hooks/useLiveClass";
// import useLogUserActivity from "@/hooks/useLogUserActivity";
import useCourse from "@/hooks/useCourse";

export default function MeetingList(): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [meetingData, setMeetingData] = useState<LiveClassResponse[]>([]);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [courseData, setCourseData] = useState<ComboData[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null);
  const [allMeetingData, setAllMeetingData] = useState<LiveClassResponse[]>([]);
  const [platformData, setPlatformData] = useState<ComboData[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string>("");

  const { liveClassList } = useLiveClass();
  // const { updateUserActivity } = useLogUserActivity();
  const { getCourseList } = useCourse();

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems(t, t("breadcrumb.meetingList"), { "": "" }),
    );

    void fetchCourseData();
    initializePlatformData();
  }, []);

  useEffect(() => {
    void fetchMeetingDataCallback();
  }, [selectedCourseId]);
  const fetchMeetingDataCallback = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const orgId = localStorage.getItem(ORG_KEY);
      const reqParams = {
        org_id: orgId as string,
        course_id: selectedCourseId,
      };
      const meetings = await liveClassList(reqParams);

      if (meetings.length > 0) {
        setAllMeetingData(meetings);
        setMeetingData(meetings);
      } else {
        setAllMeetingData([]);
        setMeetingData([]);
      }
    } catch (error) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.fetchMeetingsFailed"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCourseData = async (): Promise<void> => {
    try {
      const courses = await getCourseList("");
      if (courses.length > 0) {
        const courseOptions: ComboData[] = [
          ...courses.map((course) => ({
            value: course.course_id,
            label: course.short_name,
          })),
        ];
        setCourseData(courseOptions);
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    }
  };

  const initializePlatformData = (): void => {
    const platforms: ComboData[] = [
      { value: "", label: "All" },
      { value: "zoom", label: "Zoom" },
      { value: "gmeet", label: "Google Meet" },
    ];
    setPlatformData(platforms);
  };

  const getMeetingStatus = (meeting: LiveClassResponse): string => {
    const now = new Date();
    const endDate = new Date(meeting.end_date);
    const startDate = new Date(meeting.start_date);

    if (endDate < now) {
      return "expired";
    } else if (startDate <= now && endDate >= now) {
      return "active";
    } else if (startDate > now) {
      return "upcoming";
    } else {
      return meeting.status !== "" ? meeting.status : "unknown";
    }
  };

  const handleCourseChange = (courseId: string): void => {
    setSelectedCourseId(courseId);
    applyFilters(courseId, selectedPlatform);
  };

  const handlePlatformChange = (platform: string): void => {
    setSelectedPlatform(platform);
    applyFilters(selectedCourseId, platform);
  };

  const applyFilters = (courseId: string | null, platform: string): void => {
    let filteredMeetings = [...allMeetingData];

    // Filter by course
    if (courseId !== null && courseId !== "") {
      filteredMeetings = filteredMeetings.filter(
        (meeting) => meeting.course_id === courseId,
      );
    }

    // Filter by platform
    if (platform !== "") {
      filteredMeetings = filteredMeetings.filter(
        (meeting) =>
          meeting.meeting_type?.toLowerCase() === platform.toLowerCase(),
      );
    }

    setMeetingData(filteredMeetings);
  };

  const handleCreateMeeting = (): void => {
    router.push(pageUrl.scheduleMeeting);
  };

  const formatDateTime = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusBadge = (status: string): React.ReactNode => {
    const statusColors = {
      active: "bg-green-100 text-green-800",
      completed: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
      upcoming: "bg-blue-100 text-blue-800",
      expired: "bg-orange-100 text-orange-800",
      unknown: "bg-gray-100 text-gray-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status as keyof typeof statusColors] !== undefined
            ? statusColors[status as keyof typeof statusColors]
            : "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const columns = [
    {
      accessorKey: "meeting_type",
      header: t("meeting.platform"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) => (
        <span className="capitalize">{row.original.meeting_type}</span>
      ),
    },
    {
      accessorKey: "meeting_id",
      header: t("meeting.meetingId"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) => (
        <span className="capitalize">{row.original.meeting_id}</span>
      ),
    },
    {
      accessorKey: "meeting_url",
      header: t("meeting.meetingUrl"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) => (
        <span className="capitalize">{row.original.meeting_url}</span>
      ),
    },
    {
      accessorKey: "start_date",
      header: t("meeting.startDateTime"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) =>
        formatDateTime(row.original.start_date),
    },
    {
      accessorKey: "end_date",
      header: t("meeting.endDateTime"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) =>
        formatDateTime(row.original.end_date),
    },
    {
      accessorKey: "status",
      header: t("meeting.status"),
      cell: ({ row }: { row: { original: LiveClassResponse } }) => {
        const status = getMeetingStatus(row.original);
        return getStatusBadge(status);
      },
    },
  ];

  return (
    <MainLayout>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold tracking-tight">
          {t("meeting.LiveClasses")}
        </h1>
        <Button onClick={handleCreateMeeting} className="bg-[#9FC089]">
          <Plus className="h-4 w-4 mr-2" />
          {t("meeting.ScheduleClass")}
        </Button>
      </div>

      {/* Filters */}
      <div className="mb-4">
        <div className="flex items-center space-x-4 flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <div className="min-w-[250px]">
              <Combobox
                data={courseData}
                onSelectChange={handleCourseChange}
                placeHolder={t("meeting.selectCourse")}
                // defaultLabel={t("meeting.allCourses")}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="min-w-[200px]">
              <Combobox
                data={platformData}
                onSelectChange={handlePlatformChange}
                defaultLabel={t("meeting.selectPlatform")}
                placeHolder={t("meeting.selectPlatform")}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="border rounded-md p-4 bg-white">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={meetingData}
            FilterLabel={t("meeting.Search")}
            FilterBy="meeting_id"
            actions={[]}
          />
        )}
      </div>
    </MainLayout>
  );
}
