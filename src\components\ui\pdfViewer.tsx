"use client";
import { <PERSON><PERSON>, Worker } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import type { fileDataType } from "@/types";
import { workerurl } from "@/lib/constants";
import { convertGoogleDriveUrl, isGoogleDriveUrl } from "@/utils/googleDriveUtils";
import { useState, useEffect } from "react";

export const PdfViewer: React.FC<fileDataType> = (props): React.JSX.Element => {
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const processPdfUrl = async () => {
      try {
        setIsLoading(true);
        setError("");

        let processedUrl = props.url;

        // Handle Google Drive URLs
        if (isGoogleDriveUrl(props.url)) {
          processedUrl = convertGoogleDriveUrl(props.url);
          console.log("Converted Google Drive URL:", processedUrl);
        }

        // For Google Drive URLs, we might need to use an iframe approach
        if (isGoogleDriveUrl(props.url)) {
          setPdfUrl(processedUrl);
        } else {
          // For regular URLs, test if they're accessible
          const response = await fetch(processedUrl, { method: 'HEAD' });
          if (response.ok) {
            setPdfUrl(processedUrl);
          } else {
            throw new Error(`Failed to access PDF: ${response.status}`);
          }
        }
      } catch (error) {
        console.error("Error processing PDF URL:", error);
        setError("Failed to load PDF. Please check the URL.");
        // Fallback: try the original URL
        setPdfUrl(props.url);
      } finally {
        setIsLoading(false);
      }
    };

    if (props.url) {
      void processPdfUrl();
    }
  }, [props.url]);

  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading PDF...</p>
        </div>
      </div>
    );
  }

  if (error && !pdfUrl) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <button
            onClick={() => window.open(props.url, '_blank')}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Open in New Tab
          </button>
        </div>
      </div>
    );
  }

  // For Google Drive URLs, use iframe as fallback
  if (isGoogleDriveUrl(props.url)) {
    return (
      <div className="w-full h-full">
        <iframe
          src={pdfUrl}
          width="100%"
          height="500px"
          style={{ border: 'none' }}
          title="PDF Viewer"
        />
      </div>
    );
  }

  // For regular URLs, use react-pdf-viewer
  return (
    <div className="w-full h-full">
      <Worker workerUrl={workerurl.url}>
        <Viewer fileUrl={pdfUrl} plugins={[defaultLayoutPluginInstance]} />
      </Worker>
    </div>
  );
};
